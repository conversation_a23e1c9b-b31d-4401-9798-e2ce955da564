import { NextRequest, NextResponse } from 'next/server'
import { PurchaseModel } from '@/lib/database-supabase'
import { SupabaseAuthService } from '@/lib/auth-supabase'

export async function GET(request: NextRequest) {
  try {
    const user = await SupabaseAuthService.getCurrentUser()
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const tutorialId = searchParams.get('tutorialId')
    
    if (tutorialId) {
      // Check if user has purchased specific tutorial
      const hasPurchased = await PurchaseModel.hasUserPurchased(user.id, tutorialId)
      return NextResponse.json({
        success: true,
        data: { hasPurchased }
      })
    } else {
      // Get all purchases for user
      const purchases = await PurchaseModel.getByUserId(user.id)
      return NextResponse.json({
        success: true,
        data: purchases
      })
    }
  } catch (error: any) {
    console.error('Get purchases API error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Failed to fetch purchases' 
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await SupabaseAuthService.getCurrentUser()
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { tutorialId, paypalOrderId, amount } = await request.json()

    if (!tutorialId || !paypalOrderId || !amount) {
      return NextResponse.json(
        { success: false, error: 'Tutorial ID, PayPal order ID, and amount are required' },
        { status: 400 }
      )
    }

    const purchase = await PurchaseModel.create({
      userId: user.id,
      tutorialId,
      paypalOrderId,
      amount,
      status: 'completed'
    })

    return NextResponse.json({
      success: true,
      data: purchase
    }, { status: 201 })
  } catch (error: any) {
    console.error('Create purchase API error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Failed to create purchase' 
      },
      { status: 500 }
    )
  }
}
