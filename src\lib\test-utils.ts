/**
 * 测试工具和辅助函数
 */

import { render, RenderOptions } from '@testing-library/react'
import { ReactElement } from 'react'
import { AuthProvider } from '@/contexts/AuthContext'
import type { AppUser } from '@/types'

// 模拟用户数据
export const mockUser: AppUser = {
  id: 'test-user-id',
  email: '<EMAIL>',
  name: 'Test User',
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01')
}

// 模拟教程数据
export const mockTutorial = {
  id: 'test-tutorial-id',
  title: 'Test Magic Tutorial',
  description: 'A test tutorial for magic tricks',
  price: 29.99,
  featured: true,
  difficulty: 'beginner' as const,
  duration: 3600,
  coverImage: 'https://example.com/cover.jpg',
  preview_video_url: 'https://example.com/preview.mp4',
  videos: [],
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01')
}

// 模拟购买记录
export const mockPurchase = {
  id: 'test-purchase-id',
  userId: 'test-user-id',
  tutorialId: 'test-tutorial-id',
  amount: 29.99,
  paypalOrderId: 'test-paypal-order-id',
  status: 'completed',
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01')
}

// 自定义渲染函数，包含必要的 Provider
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  initialUser?: AppUser | null
}

export function customRender(
  ui: ReactElement,
  options: CustomRenderOptions = {}
) {
  const { initialUser = null, ...renderOptions } = options

  // 创建包装器组件
  function Wrapper({ children }: { children: React.ReactNode }) {
    return (
      <AuthProvider>
        {children}
      </AuthProvider>
    )
  }

  return render(ui, { wrapper: Wrapper, ...renderOptions })
}

// 重新导出所有 testing-library 工具
export * from '@testing-library/react'
export { customRender as render }

// API 模拟工具
export class ApiMocker {
  private static originalFetch = global.fetch

  // 模拟成功响应
  static mockSuccess(data: any, status = 200) {
    global.fetch = jest.fn().mockResolvedValue({
      ok: true,
      status,
      json: async () => ({ success: true, data })
    })
  }

  // 模拟错误响应
  static mockError(error: string, status = 400) {
    global.fetch = jest.fn().mockResolvedValue({
      ok: false,
      status,
      json: async () => ({ success: false, error })
    })
  }

  // 模拟网络错误
  static mockNetworkError() {
    global.fetch = jest.fn().mockRejectedValue(new Error('Network error'))
  }

  // 恢复原始 fetch
  static restore() {
    global.fetch = this.originalFetch
  }
}

// Supabase 模拟工具
export class SupabaseMocker {
  static mockAuthUser(user: Partial<AppUser> | null = mockUser) {
    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({
          data: { user },
          error: null
        }),
        signInWithPassword: jest.fn().mockResolvedValue({
          data: { user, session: { access_token: 'mock-token' } },
          error: null
        }),
        signUp: jest.fn().mockResolvedValue({
          data: { user, session: { access_token: 'mock-token' } },
          error: null
        }),
        signOut: jest.fn().mockResolvedValue({
          error: null
        })
      },
      from: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      single: jest.fn().mockResolvedValue({
        data: user,
        error: null
      }),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis()
    }

    return mockSupabase
  }

  static mockTutorialQueries(tutorials = [mockTutorial]) {
    return {
      from: jest.fn(() => ({
        select: jest.fn(() => ({
          order: jest.fn(() => ({
            data: tutorials,
            error: null
          })),
          eq: jest.fn(() => ({
            single: jest.fn().mockResolvedValue({
              data: tutorials[0],
              error: null
            })
          }))
        }))
      }))
    }
  }
}

// 测试数据生成器
export class TestDataGenerator {
  // 生成随机用户
  static generateUser(overrides: Partial<AppUser> = {}): AppUser {
    return {
      id: `user-${Math.random().toString(36).substr(2, 9)}`,
      email: `test${Math.random().toString(36).substr(2, 5)}@example.com`,
      name: `Test User ${Math.random().toString(36).substr(2, 5)}`,
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides
    }
  }

  // 生成随机教程
  static generateTutorial(overrides: any = {}) {
    return {
      id: `tutorial-${Math.random().toString(36).substr(2, 9)}`,
      title: `Magic Tutorial ${Math.random().toString(36).substr(2, 5)}`,
      description: 'A comprehensive magic tutorial',
      price: Math.floor(Math.random() * 100) + 10,
      featured: Math.random() > 0.5,
      difficulty: ['beginner', 'intermediate', 'advanced'][Math.floor(Math.random() * 3)],
      duration: Math.floor(Math.random() * 7200) + 1800,
      coverImage: 'https://example.com/cover.jpg',
      preview_video_url: 'https://example.com/preview.mp4',
      videos: [],
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides
    }
  }

  // 生成多个测试数据
  static generateMultiple<T>(generator: () => T, count: number): T[] {
    return Array.from({ length: count }, generator)
  }
}

// 异步测试工具
export class AsyncTestUtils {
  // 等待条件满足
  static async waitFor(
    condition: () => boolean | Promise<boolean>,
    timeout = 5000,
    interval = 100
  ): Promise<void> {
    const startTime = Date.now()
    
    while (Date.now() - startTime < timeout) {
      if (await condition()) {
        return
      }
      await this.sleep(interval)
    }
    
    throw new Error(`Condition not met within ${timeout}ms`)
  }

  // 延迟执行
  static sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  // 模拟用户交互延迟
  static async simulateUserDelay(min = 100, max = 500): Promise<void> {
    const delay = Math.floor(Math.random() * (max - min + 1)) + min
    await this.sleep(delay)
  }
}

// 性能测试工具
export class PerformanceTestUtils {
  // 测量函数执行时间
  static async measureTime<T>(fn: () => Promise<T>): Promise<{ result: T; duration: number }> {
    const start = performance.now()
    const result = await fn()
    const duration = performance.now() - start
    
    return { result, duration }
  }

  // 测量多次执行的平均时间
  static async measureAverageTime<T>(
    fn: () => Promise<T>,
    iterations = 10
  ): Promise<{ averageDuration: number; results: T[] }> {
    const results: T[] = []
    let totalDuration = 0

    for (let i = 0; i < iterations; i++) {
      const { result, duration } = await this.measureTime(fn)
      results.push(result)
      totalDuration += duration
    }

    return {
      averageDuration: totalDuration / iterations,
      results
    }
  }
}

// 错误测试工具
export class ErrorTestUtils {
  // 测试错误处理
  static async expectError(
    fn: () => Promise<any>,
    expectedError?: string | RegExp
  ): Promise<Error> {
    try {
      await fn()
      throw new Error('Expected function to throw an error')
    } catch (error) {
      if (expectedError) {
        if (typeof expectedError === 'string') {
          expect(error.message).toContain(expectedError)
        } else {
          expect(error.message).toMatch(expectedError)
        }
      }
      return error as Error
    }
  }

  // 测试异步错误处理
  static async expectAsyncError<T>(
    promise: Promise<T>,
    expectedError?: string | RegExp
  ): Promise<Error> {
    return this.expectError(() => promise, expectedError)
  }
}

// 清理工具
export class TestCleanup {
  private static cleanupFunctions: Array<() => void | Promise<void>> = []

  // 注册清理函数
  static register(cleanupFn: () => void | Promise<void>): void {
    this.cleanupFunctions.push(cleanupFn)
  }

  // 执行所有清理函数
  static async cleanup(): Promise<void> {
    for (const cleanupFn of this.cleanupFunctions) {
      await cleanupFn()
    }
    this.cleanupFunctions = []
  }

  // 在测试后自动清理
  static afterEach(): void {
    afterEach(async () => {
      await this.cleanup()
      ApiMocker.restore()
    })
  }
}

// 自动设置清理
TestCleanup.afterEach()
