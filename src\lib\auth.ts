import jwt from 'jsonwebtoken'
import { cookies } from 'next/headers'
import { UserModel } from './database'
import type { User } from '@/types'

// TODO: In production, use environment variables for JWT secret
// For Supabase integration, this would be replaced with Supabase Auth
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production'
const JWT_EXPIRES_IN = '7d'

export interface AuthTokenPayload {
  userId: string
  email: string
}

export class AuthService {
  static generateToken(user: User): string {
    const payload: AuthTokenPayload = {
      userId: user.id,
      email: user.email,
    }
    
    return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN })
  }
  
  static verifyToken(token: string): AuthTokenPayload | null {
    try {
      return jwt.verify(token, JWT_SECRET) as AuthTokenPayload
    } catch (error) {
      console.error('Token verification failed:', error)
      return null
    }
  }
  
  static async getCurrentUser(): Promise<User | null> {
    try {
      const cookieStore = cookies()
      const token = cookieStore.get('auth-token')?.value
      
      if (!token) return null
      
      const payload = this.verifyToken(token)
      if (!payload) return null
      
      return await UserModel.findById(payload.userId)
    } catch (error) {
      console.error('Get current user failed:', error)
      return null
    }
  }
  
  static async login(email: string, password: string): Promise<{ user: User; token: string } | null> {
    try {
      const user = await UserModel.verifyPassword(email, password)
      if (!user) return null
      
      const token = this.generateToken(user)
      return { user, token }
    } catch (error) {
      console.error('Login failed:', error)
      return null
    }
  }
  
  static async register(userData: {
    email: string
    password: string
    name?: string
  }): Promise<{ user: User; token: string }> {
    try {
      const user = await UserModel.create(userData)
      const token = this.generateToken(user)
      return { user, token }
    } catch (error) {
      console.error('Registration failed:', error)
      throw error
    }
  }
  
  // Placeholder for PayPal integration
  static async createUserFromPayPal(paypalEmail: string): Promise<User> {
    try {
      // Check if user already exists
      const existingUser = await UserModel.findByEmail(paypalEmail)
      if (existingUser) {
        return existingUser
      }
      
      // Create new user with PayPal email
      // In production, this would integrate with PayPal API to get user details
      const tempPassword = Math.random().toString(36).substring(2, 15)
      const user = await UserModel.create({
        email: paypalEmail,
        password: tempPassword, // User can reset password later
        name: paypalEmail.split('@')[0], // Use email prefix as default name
      })
      
      // TODO: Send welcome email with password reset link
      console.log(`Created user from PayPal: ${paypalEmail}`)
      
      return user
    } catch (error) {
      console.error('PayPal user creation failed:', error)
      throw error
    }
  }
}

// Middleware helper for protected routes
export async function requireAuth(): Promise<User> {
  const user = await AuthService.getCurrentUser()
  if (!user) {
    throw new Error('Authentication required')
  }
  return user
}

// Helper for checking if user has purchased a tutorial
export async function checkTutorialAccess(userId: string, tutorialId: string): Promise<boolean> {
  const { PurchaseModel } = await import('./database')
  return await PurchaseModel.hasUserPurchased(userId, tutorialId)
}
