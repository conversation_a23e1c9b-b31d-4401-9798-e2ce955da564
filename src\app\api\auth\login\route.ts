import { NextRequest, NextResponse } from 'next/server'
import { SupabaseAuthService } from '@/lib/auth-supabase'
import { validateLoginData, SecurityUtils } from '@/lib/validation'

export async function POST(request: NextRequest) {
  try {
    // 速率限制检查
    const clientIP = SecurityUtils.getClientIP(request)
    if (!SecurityUtils.checkRateLimit(clientIP, 10, 60000)) { // 每分钟最多10次登录尝试
      return NextResponse.json(
        { success: false, error: 'Too many login attempts. Please try again later.' },
        { status: 429 }
      )
    }

    const requestData = await request.json()

    // 输入验证
    const validation = validateLoginData(requestData)
    if (!validation.isValid) {
      return NextResponse.json(
        { success: false, error: validation.errors.join(', ') },
        { status: 400 }
      )
    }

    const { email, password } = requestData

    const { user, session } = await SupabaseAuthService.signIn(email, password)

    // 获取用户 profile 信息
    const appUser = await SupabaseAuthService.getCurrentUser()

    return NextResponse.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          name: appUser?.name || user.email.split('@')[0]
        },
        session: {
          access_token: session.access_token,
          refresh_token: session.refresh_token
        }
      }
    })
  } catch (error: any) {
    console.error('Login API error:', error)

    // 使用安全的错误响应
    const safeError = SecurityUtils.createSafeErrorResponse(error, process.env.NODE_ENV === 'development')

    return NextResponse.json(
      safeError,
      { status: 401 }
    )
  }
}
