/**
 * 统一的数据库服务层
 * 提供一致的数据访问接口，隐藏具体的数据库实现细节
 */

import { supabase } from '../supabase'
import { CacheManager, withCache, PerformanceMonitor } from '../performance'
import type {
  AppUser,
  Tutorial,
  Video,
  Purchase,
  BlogPost,
  PortfolioItem
} from '@/types'

// 基础数据库操作接口
interface BaseModel<T> {
  getAll(): Promise<T[]>
  getById(id: string): Promise<T | null>
  create(data: Partial<T>): Promise<T>
  update(id: string, data: Partial<T>): Promise<T | null>
  delete(id: string): Promise<boolean>
}

// 用户模型
export class UserModel implements Partial<BaseModel<AppUser>> {
  static async getById(id: string): Promise<AppUser | null> {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', id)
      .single()

    if (error || !data) return null

    return {
      id: data.id,
      email: data.email,
      name: data.name,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    }
  }

  static async create(userData: { id: string; email: string; name?: string }): Promise<AppUser> {
    const { data, error } = await supabase
      .from('profiles')
      .insert({
        id: userData.id,
        email: userData.email,
        name: userData.name || userData.email.split('@')[0],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) throw error

    return {
      id: data.id,
      email: data.email,
      name: data.name,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    }
  }

  static async update(id: string, updateData: Partial<AppUser>): Promise<AppUser | null> {
    const { data, error } = await supabase
      .from('profiles')
      .update({
        name: updateData.name,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    if (error || !data) return null

    return {
      id: data.id,
      email: data.email,
      name: data.name,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    }
  }
}

// 教程模型
export class TutorialModel implements BaseModel<Tutorial> {
  async getAll(): Promise<Tutorial[]> {
    return PerformanceMonitor.time('TutorialModel.getAll', async () => {
      // 尝试从缓存获取
      const cacheKey = 'tutorials:all'
      const cached = CacheManager.get<Tutorial[]>(cacheKey)
      if (cached) {
        return cached
      }

      const { data, error } = await supabase
        .from('tutorials')
        .select(`
          *,
          videos (*)
        `)
        .order('created_at', { ascending: false })

      if (error) throw error

      const tutorials = data.map(this.mapToTutorial)

      // 缓存结果（5分钟）
      CacheManager.set(cacheKey, tutorials, 300000)

      return tutorials
    })
  }

  async getById(id: string): Promise<Tutorial | null> {
    return PerformanceMonitor.time('TutorialModel.getById', async () => {
      // 尝试从缓存获取
      const cacheKey = `tutorial:${id}`
      const cached = CacheManager.get<Tutorial>(cacheKey)
      if (cached) {
        return cached
      }

      const { data, error } = await supabase
        .from('tutorials')
        .select(`
          *,
          videos (*)
        `)
        .eq('id', id)
        .single()

      if (error || !data) return null

      const tutorial = this.mapToTutorial(data)

      // 缓存结果（10分钟）
      CacheManager.set(cacheKey, tutorial, 600000)

      return tutorial
    })
  }

  async getFeatured(): Promise<Tutorial[]> {
    const { data, error } = await supabase
      .from('tutorials')
      .select(`
        *,
        videos (*)
      `)
      .eq('featured', true)
      .order('created_at', { ascending: false })

    if (error) throw error

    return data.map(this.mapToTutorial)
  }

  async create(tutorialData: Partial<Tutorial>): Promise<Tutorial> {
    const { data, error } = await supabase
      .from('tutorials')
      .insert({
        title: tutorialData.title,
        description: tutorialData.description,
        price: tutorialData.price,
        featured: tutorialData.featured || false,
        difficulty: tutorialData.difficulty || 'beginner',
        duration: tutorialData.duration || 0,
        cover_image: tutorialData.coverImage,
        preview_video_url: tutorialData.preview_video_url,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) throw error

    return this.mapToTutorial({ ...data, videos: [] })
  }

  async update(id: string, updateData: Partial<Tutorial>): Promise<Tutorial | null> {
    const { data, error } = await supabase
      .from('tutorials')
      .update({
        title: updateData.title,
        description: updateData.description,
        price: updateData.price,
        featured: updateData.featured,
        difficulty: updateData.difficulty,
        duration: updateData.duration,
        cover_image: updateData.coverImage,
        preview_video_url: updateData.preview_video_url,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select(`
        *,
        videos (*)
      `)
      .single()

    if (error || !data) return null

    return this.mapToTutorial(data)
  }

  async delete(id: string): Promise<boolean> {
    const { error } = await supabase
      .from('tutorials')
      .delete()
      .eq('id', id)

    return !error
  }

  private mapToTutorial(data: any): Tutorial {
    return {
      id: data.id,
      title: data.title,
      description: data.description,
      price: data.price,
      featured: data.featured,
      difficulty: data.difficulty,
      duration: data.duration,
      coverImage: data.cover_image,
      preview_video_url: data.preview_video_url,
      videos: data.videos?.map((video: any) => ({
        id: video.id,
        tutorialId: video.tutorial_id,
        title: video.title,
        description: video.description,
        duration: video.duration,
        videoUrl: video.video_url,
        thumbnailUrl: video.thumbnail_url,
        order: video.order_index,
        createdAt: new Date(video.created_at),
        updatedAt: new Date(video.updated_at)
      })) || [],
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    }
  }
}

// 购买记录模型
export class PurchaseModel {
  static async getByUserId(userId: string): Promise<Purchase[]> {
    const { data, error } = await supabase
      .from('purchases')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    if (error) throw error

    return data.map(purchase => ({
      id: purchase.id,
      userId: purchase.user_id,
      tutorialId: purchase.tutorial_id,
      amount: purchase.amount,
      paypalOrderId: purchase.paypal_order_id,
      status: purchase.status,
      createdAt: new Date(purchase.created_at),
      updatedAt: new Date(purchase.updated_at)
    }))
  }

  static async hasUserPurchased(userId: string, tutorialId: string): Promise<boolean> {
    const { data, error } = await supabase
      .from('purchases')
      .select('id')
      .eq('user_id', userId)
      .eq('tutorial_id', tutorialId)
      .eq('status', 'completed')
      .single()

    return !error && !!data
  }

  static async create(purchaseData: {
    userId: string
    tutorialId: string
    paypalOrderId: string
    amount: number
    status: string
  }): Promise<Purchase> {
    const { data, error } = await supabase
      .from('purchases')
      .insert({
        user_id: purchaseData.userId,
        tutorial_id: purchaseData.tutorialId,
        paypal_order_id: purchaseData.paypalOrderId,
        amount: purchaseData.amount,
        status: purchaseData.status,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) throw error

    return {
      id: data.id,
      userId: data.user_id,
      tutorialId: data.tutorial_id,
      amount: data.amount,
      paypalOrderId: data.paypal_order_id,
      status: data.status,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    }
  }
}

// 导出实例
export const tutorialModel = new TutorialModel()
