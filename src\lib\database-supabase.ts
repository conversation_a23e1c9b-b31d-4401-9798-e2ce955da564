import { supabase } from './supabase'
import type { 
  User, 
  Tutorial, 
  Video, 
  Purchase, 
  BlogPost, 
  PortfolioItem
} from '@/types'

// Supabase Database Service Layer
// Replaces the previous JSON-based mock database with real Supabase operations

export async function initializeDatabase() {
  // For Supabase, initialization is handled by the schema setup
  // This function is kept for compatibility but doesn't need to do anything
  console.log('Using Supabase database - no local initialization needed')
}

// User operations
export class UserModel {
  static async create(userData: { email: string; password: string; name?: string }): Promise<User> {
    // User creation is handled by Supabase Auth, this method creates the profile
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: userData.email,
      password: userData.password,
      options: {
        data: {
          name: userData.name || userData.email.split('@')[0]
        }
      }
    })

    if (authError) throw authError
    if (!authData.user) throw new Error('User creation failed')

    // Profile is automatically created by the trigger, so we just return the user data
    return {
      id: authData.user.id,
      email: authData.user.email!,
      name: userData.name || userData.email.split('@')[0],
      createdAt: new Date(authData.user.created_at),
      updatedAt: new Date(authData.user.created_at)
    }
  }

  static async findByEmail(email: string): Promise<User | null> {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('email', email)
      .single()

    if (error) {
      if (error.code === 'PGRST116') return null // No rows returned
      throw error
    }

    return {
      id: data.id,
      email: data.email,
      name: data.name || data.email.split('@')[0],
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    }
  }

  static async findById(id: string): Promise<User | null> {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') return null // No rows returned
      throw error
    }

    return {
      id: data.id,
      email: data.email,
      name: data.name || data.email.split('@')[0],
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    }
  }

  static async verifyPassword(email: string, password: string): Promise<User | null> {
    // Password verification is handled by Supabase Auth
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })

    if (error) return null
    if (!data.user) return null

    // Get the profile data
    const profile = await this.findById(data.user.id)
    return profile
  }
}

// Tutorial operations
export class TutorialModel {
  static async create(tutorialData: {
    title: string
    description: string
    price: number
    coverImage?: string
  }): Promise<Tutorial> {
    const { data, error } = await supabase
      .from('tutorials')
      .insert({
        title: tutorialData.title,
        description: tutorialData.description,
        price: tutorialData.price,
        cover_image: tutorialData.coverImage || null
      })
      .select()
      .single()

    if (error) throw error

    return {
      id: data.id,
      title: data.title,
      description: data.description,
      price: data.price,
      featured: data.featured,
      difficulty: data.difficulty as 'beginner' | 'intermediate' | 'advanced',
      duration: data.duration,
      coverImage: data.cover_image || undefined,
      preview_video_url: data.preview_video_url || undefined,
      videos: [], // Will be populated by findAll or findById
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    }
  }

  static async findAll(): Promise<Tutorial[]> {
    // Get tutorials
    const { data: tutorials, error: tutorialsError } = await supabase
      .from('tutorials')
      .select('*')
      .order('created_at', { ascending: false })

    if (tutorialsError) throw tutorialsError

    // Get all videos
    const { data: videos, error: videosError } = await supabase
      .from('videos')
      .select('*')
      .order('order_index', { ascending: true })

    if (videosError) throw videosError

    // Combine tutorials with their videos
    return tutorials.map(tutorial => ({
      id: tutorial.id,
      title: tutorial.title,
      description: tutorial.description,
      price: tutorial.price,
      featured: tutorial.featured,
      difficulty: tutorial.difficulty as 'beginner' | 'intermediate' | 'advanced',
      duration: tutorial.duration,
      coverImage: tutorial.cover_image || undefined,
      preview_video_url: tutorial.preview_video_url || undefined,
      videos: videos
        .filter(v => v.tutorial_id === tutorial.id)
        .sort((a, b) => a.order_index - b.order_index)
        .map(v => ({
          id: v.id,
          title: v.title,
          description: v.description || undefined,
          duration: v.duration,
          videoUrl: v.video_url,
          thumbnailUrl: v.thumbnail_url || undefined,
          order: v.order_index,
          tutorialId: v.tutorial_id,
          createdAt: new Date(v.created_at),
          updatedAt: new Date(v.updated_at)
        })),
      createdAt: new Date(tutorial.created_at),
      updatedAt: new Date(tutorial.updated_at)
    }))
  }

  static async findById(id: string): Promise<Tutorial | null> {
    const tutorials = await this.findAll()
    return tutorials.find(t => t.id === id) || null
  }

  // Alias for findById for consistency with application code
  static async getById(id: string): Promise<Tutorial | null> {
    return this.findById(id)
  }
}

// Video operations
export class VideoModel {
  static async create(videoData: {
    title: string
    description?: string
    duration: number
    videoUrl: string
    thumbnailUrl?: string
    order: number
    tutorialId: string
  }): Promise<Video> {
    const { data, error } = await supabase
      .from('videos')
      .insert({
        title: videoData.title,
        description: videoData.description || null,
        duration: videoData.duration,
        video_url: videoData.videoUrl,
        thumbnail_url: videoData.thumbnailUrl || null,
        order_index: videoData.order,
        tutorial_id: videoData.tutorialId
      })
      .select()
      .single()

    if (error) throw error

    return {
      id: data.id,
      title: data.title,
      description: data.description || undefined,
      duration: data.duration,
      videoUrl: data.video_url,
      thumbnailUrl: data.thumbnail_url || undefined,
      order: data.order_index,
      tutorialId: data.tutorial_id
    }
  }

  static async findByTutorialId(tutorialId: string): Promise<Video[]> {
    const { data, error } = await supabase
      .from('videos')
      .select('*')
      .eq('tutorial_id', tutorialId)
      .order('order_index', { ascending: true })

    if (error) throw error

    return data.map(v => ({
      id: v.id,
      title: v.title,
      description: v.description || undefined,
      duration: v.duration,
      videoUrl: v.video_url,
      thumbnailUrl: v.thumbnail_url || undefined,
      order: v.order_index,
      tutorialId: v.tutorial_id
    }))
  }
}

// Purchase operations
export class PurchaseModel {
  static async create(purchaseData: {
    userId: string
    tutorialId: string
    amount: number
    paypalOrderId: string
  }): Promise<Purchase> {
    const { data, error } = await supabase
      .from('purchases')
      .insert({
        user_id: purchaseData.userId,
        tutorial_id: purchaseData.tutorialId,
        amount: purchaseData.amount,
        paypal_order_id: purchaseData.paypalOrderId,
        status: 'completed'
      })
      .select()
      .single()

    if (error) throw error

    return {
      id: data.id,
      userId: data.user_id,
      tutorialId: data.tutorial_id,
      amount: data.amount,
      paypalOrderId: data.paypal_order_id,
      status: data.status as 'pending' | 'completed' | 'failed',
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    }
  }

  static async findByUserId(userId: string): Promise<Purchase[]> {
    const { data, error } = await supabase
      .from('purchases')
      .select('*')
      .eq('user_id', userId)
      .eq('status', 'completed') // Only return completed purchases
      .order('created_at', { ascending: false })

    if (error) throw error

    return data.map(p => ({
      id: p.id,
      userId: p.user_id,
      tutorialId: p.tutorial_id,
      amount: p.amount,
      paypalOrderId: p.paypal_order_id,
      status: p.status as 'pending' | 'completed' | 'failed',
      createdAt: new Date(p.created_at),
      updatedAt: new Date(p.updated_at)
    }))
  }

  static async hasUserPurchased(userId: string, tutorialId: string): Promise<boolean> {
    const { data, error } = await supabase
      .from('purchases')
      .select('id')
      .eq('user_id', userId)
      .eq('tutorial_id', tutorialId)
      .eq('status', 'completed')
      .limit(1)

    if (error) throw error

    return data.length > 0
  }

  // Alias for findByUserId for consistency
  static async getUserPurchases(userId: string): Promise<Purchase[]> {
    return this.findByUserId(userId)
  }
}

// Blog Post operations
export class BlogPostModel {
  static async create(postData: {
    title: string
    content: string
    excerpt: string
    featured?: boolean
    published?: boolean
    author: string
    tags?: string[]
  }): Promise<BlogPost> {
    const { data, error } = await supabase
      .from('blog_posts')
      .insert({
        title: postData.title,
        content: postData.content,
        excerpt: postData.excerpt,
        featured: postData.featured || false,
        published: postData.published || false,
        author: postData.author,
        tags: postData.tags || []
      })
      .select()
      .single()

    if (error) throw error

    return {
      id: data.id,
      title: data.title,
      content: data.content,
      excerpt: data.excerpt,
      featured: data.featured,
      published: data.published,
      author: data.author,
      tags: data.tags || [],
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    }
  }

  static async findAll(): Promise<BlogPost[]> {
    const { data, error } = await supabase
      .from('blog_posts')
      .select('*')
      .eq('published', true)
      .order('created_at', { ascending: false })

    if (error) throw error

    return data.map(post => ({
      id: post.id,
      title: post.title,
      content: post.content,
      excerpt: post.excerpt,
      featured: post.featured,
      published: post.published,
      author: post.author,
      tags: post.tags || [],
      createdAt: new Date(post.created_at),
      updatedAt: new Date(post.updated_at)
    }))
  }

  static async findById(id: string): Promise<BlogPost | null> {
    const { data, error } = await supabase
      .from('blog_posts')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') return null // No rows returned
      throw error
    }

    return {
      id: data.id,
      title: data.title,
      content: data.content,
      excerpt: data.excerpt,
      featured: data.featured,
      published: data.published,
      author: data.author,
      tags: data.tags || [],
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    }
  }
}

// Portfolio operations
export class PortfolioModel {
  static async create(itemData: {
    title: string
    description: string
    imageUrl: string
    category: string
    featured?: boolean
    date: string
  }): Promise<PortfolioItem> {
    const { data, error } = await supabase
      .from('portfolio')
      .insert({
        title: itemData.title,
        description: itemData.description,
        image_url: itemData.imageUrl,
        category: itemData.category,
        featured: itemData.featured || false,
        date: itemData.date
      })
      .select()
      .single()

    if (error) throw error

    return {
      id: data.id,
      title: data.title,
      description: data.description,
      imageUrl: data.image_url,
      category: data.category,
      featured: data.featured,
      date: data.date,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    }
  }

  static async findAll(): Promise<PortfolioItem[]> {
    const { data, error } = await supabase
      .from('portfolio')
      .select('*')
      .order('date', { ascending: false })

    if (error) throw error

    return data.map(item => ({
      id: item.id,
      title: item.title,
      description: item.description,
      imageUrl: item.image_url,
      category: item.category,
      featured: item.featured,
      date: item.date,
      createdAt: new Date(item.created_at),
      updatedAt: new Date(item.updated_at)
    }))
  }
}
