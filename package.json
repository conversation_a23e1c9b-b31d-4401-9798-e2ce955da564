{"name": "magic-academy", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "setup-supabase": "node scripts/quick-setup.js", "check-supabase": "npx ts-node scripts/check-supabase.ts", "seed-supabase": "npx ts-node scripts/seed-supabase.ts"}, "dependencies": {"@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.52.1", "@types/bcryptjs": "^2.4.0", "@types/jsonwebtoken": "^9.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/uuid": "^9.0.0", "autoprefixer": "^10.4.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.0", "next": "^14.0.0", "postcss": "^8.4.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-markdown": "^10.1.0", "rehype-highlight": "^7.0.2", "remark-gfm": "^4.0.1", "tailwindcss": "^3.3.0", "typescript": "^5.0.0", "uuid": "^9.0.0"}, "devDependencies": {"@types/node": "^24.1.0", "@testing-library/jest-dom": "^6.1.0", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.5.0", "dotenv": "^17.2.1", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "ts-node": "^10.9.2"}}