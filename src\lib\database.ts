import fs from 'fs/promises'
import path from 'path'
import { v4 as uuidv4 } from 'uuid'
import bcrypt from 'bcryptjs'
import type { 
  User, 
  Tutorial, 
  Video, 
  Purchase, 
  BlogPost, 
  PortfolioItem,
  DbUser,
  DbTutorial,
  DbVideo,
  DbPurchase
} from '@/types'

// TODO: Replace with SQLite/Supabase integration
// This is a simple JSON-based database for local development
// In production, this would be replaced with:
// - SQLite for local development
// - Supabase for production (as specified in PRD)

const DB_PATH = path.join(process.cwd(), 'data')
const USERS_FILE = path.join(DB_PATH, 'users.json')
const TUTORIALS_FILE = path.join(DB_PATH, 'tutorials.json')
const VIDEOS_FILE = path.join(DB_PATH, 'videos.json')
const PURCHASES_FILE = path.join(DB_PATH, 'purchases.json')
const BLOG_POSTS_FILE = path.join(DB_PATH, 'blog_posts.json')
const PORTFOLIO_FILE = path.join(DB_PATH, 'portfolio.json')

// Initialize database files
export async function initializeDatabase() {
  try {
    await fs.mkdir(DB_PATH, { recursive: true })
    
    // Initialize empty files if they don't exist
    const files = [
      { path: USERS_FILE, data: [] },
      { path: TUTORIALS_FILE, data: [] },
      { path: VIDEOS_FILE, data: [] },
      { path: PURCHASES_FILE, data: [] },
      { path: BLOG_POSTS_FILE, data: [] },
      { path: PORTFOLIO_FILE, data: [] },
    ]
    
    for (const file of files) {
      try {
        await fs.access(file.path)
      } catch {
        await fs.writeFile(file.path, JSON.stringify(file.data, null, 2))
      }
    }
    
    console.log('Database initialized successfully')
  } catch (error) {
    console.error('Failed to initialize database:', error)
    throw error
  }
}

// Generic file operations
async function readJsonFile<T>(filePath: string): Promise<T[]> {
  try {
    const data = await fs.readFile(filePath, 'utf-8')
    return JSON.parse(data)
  } catch (error) {
    console.error(`Error reading ${filePath}:`, error)
    return []
  }
}

async function writeJsonFile<T>(filePath: string, data: T[]): Promise<void> {
  try {
    await fs.writeFile(filePath, JSON.stringify(data, null, 2))
  } catch (error) {
    console.error(`Error writing ${filePath}:`, error)
    throw error
  }
}

// User operations
export class UserModel {
  static async create(userData: { email: string; password: string; name?: string }): Promise<User> {
    const users = await readJsonFile<DbUser>(USERS_FILE)
    
    // Check if user already exists
    const existingUser = users.find(u => u.email === userData.email)
    if (existingUser) {
      throw new Error('User already exists')
    }
    
    const hashedPassword = await bcrypt.hash(userData.password, 12)
    const now = new Date().toISOString()
    
    const newUser: DbUser = {
      id: uuidv4(),
      email: userData.email,
      password_hash: hashedPassword,
      name: userData.name || null,
      created_at: now,
      updated_at: now,
    }
    
    users.push(newUser)
    await writeJsonFile(USERS_FILE, users)
    
    return {
      id: newUser.id,
      email: newUser.email,
      name: newUser.name || undefined,
      createdAt: new Date(newUser.created_at),
      updatedAt: new Date(newUser.updated_at),
    }
  }
  
  static async findByEmail(email: string): Promise<(User & { passwordHash: string }) | null> {
    const users = await readJsonFile<DbUser>(USERS_FILE)
    const user = users.find(u => u.email === email)
    
    if (!user) return null
    
    return {
      id: user.id,
      email: user.email,
      name: user.name || undefined,
      passwordHash: user.password_hash,
      createdAt: new Date(user.created_at),
      updatedAt: new Date(user.updated_at),
    }
  }
  
  static async findById(id: string): Promise<User | null> {
    const users = await readJsonFile<DbUser>(USERS_FILE)
    const user = users.find(u => u.id === id)
    
    if (!user) return null
    
    return {
      id: user.id,
      email: user.email,
      name: user.name || undefined,
      createdAt: new Date(user.created_at),
      updatedAt: new Date(user.updated_at),
    }
  }
  
  static async verifyPassword(email: string, password: string): Promise<User | null> {
    const user = await this.findByEmail(email)
    if (!user) return null
    
    const isValid = await bcrypt.compare(password, user.passwordHash)
    if (!isValid) return null
    
    return {
      id: user.id,
      email: user.email,
      name: user.name,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    }
  }
}

// Tutorial operations
export class TutorialModel {
  static async create(tutorialData: {
    title: string
    description: string
    price: number
    coverImage?: string
  }): Promise<Tutorial> {
    const tutorials = await readJsonFile<DbTutorial>(TUTORIALS_FILE)
    const now = new Date().toISOString()
    
    const newTutorial: DbTutorial = {
      id: uuidv4(),
      title: tutorialData.title,
      description: tutorialData.description,
      price: tutorialData.price,
      cover_image: tutorialData.coverImage || null,
      created_at: now,
      updated_at: now,
    }
    
    tutorials.push(newTutorial)
    await writeJsonFile(TUTORIALS_FILE, tutorials)
    
    return {
      id: newTutorial.id,
      title: newTutorial.title,
      description: newTutorial.description,
      price: newTutorial.price,
      coverImage: newTutorial.cover_image || undefined,
      videos: [], // Will be populated separately
      createdAt: new Date(newTutorial.created_at),
      updatedAt: new Date(newTutorial.updated_at),
    }
  }
  
  static async findAll(): Promise<Tutorial[]> {
    const tutorials = await readJsonFile<DbTutorial>(TUTORIALS_FILE)
    const videos = await readJsonFile<DbVideo>(VIDEOS_FILE)
    
    return tutorials.map(tutorial => ({
      id: tutorial.id,
      title: tutorial.title,
      description: tutorial.description,
      price: tutorial.price,
      coverImage: tutorial.cover_image || undefined,
      videos: videos
        .filter(v => v.tutorial_id === tutorial.id)
        .sort((a, b) => a.order_index - b.order_index)
        .map(v => ({
          id: v.id,
          title: v.title,
          description: v.description || undefined,
          duration: v.duration,
          videoUrl: v.video_url,
          thumbnailUrl: v.thumbnail_url || undefined,
          order: v.order_index,
          tutorialId: v.tutorial_id,
        })),
      createdAt: new Date(tutorial.created_at),
      updatedAt: new Date(tutorial.updated_at),
    }))
  }
  
  static async findById(id: string): Promise<Tutorial | null> {
    const tutorials = await this.findAll()
    return tutorials.find(t => t.id === id) || null
  }
}

// Purchase operations
export class PurchaseModel {
  static async create(purchaseData: {
    userId: string
    tutorialId: string
    amount: number
    paymentId: string
  }): Promise<Purchase> {
    const purchases = await readJsonFile<DbPurchase>(PURCHASES_FILE)
    const now = new Date().toISOString()
    
    const newPurchase: DbPurchase = {
      id: uuidv4(),
      user_id: purchaseData.userId,
      tutorial_id: purchaseData.tutorialId,
      amount: purchaseData.amount,
      payment_id: purchaseData.paymentId,
      status: 'completed',
      created_at: now,
    }
    
    purchases.push(newPurchase)
    await writeJsonFile(PURCHASES_FILE, purchases)
    
    return {
      id: newPurchase.id,
      userId: newPurchase.user_id,
      tutorialId: newPurchase.tutorial_id,
      amount: newPurchase.amount,
      paymentId: newPurchase.payment_id,
      status: 'completed',
      createdAt: new Date(newPurchase.created_at),
    }
  }
  
  static async findByUserId(userId: string): Promise<Purchase[]> {
    const purchases = await readJsonFile<DbPurchase>(PURCHASES_FILE)
    
    return purchases
      .filter(p => p.user_id === userId)
      .map(p => ({
        id: p.id,
        userId: p.user_id,
        tutorialId: p.tutorial_id,
        amount: p.amount,
        paymentId: p.payment_id,
        status: p.status as 'pending' | 'completed' | 'failed',
        createdAt: new Date(p.created_at),
      }))
  }
  
  static async hasUserPurchased(userId: string, tutorialId: string): Promise<boolean> {
    const purchases = await readJsonFile<DbPurchase>(PURCHASES_FILE)
    return purchases.some(p => 
      p.user_id === userId && 
      p.tutorial_id === tutorialId && 
      p.status === 'completed'
    )
  }
}
